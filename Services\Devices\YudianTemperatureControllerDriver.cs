using System;
using System.Threading.Tasks;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 宇电 MK008 温控器驱动
    /// 支持 Modbus RTU 通信协议，提供完整的温度控制和监测功能
    /// </summary>
    /// <remarks>
    /// 该驱动程序适用于宇电MK008系列温控器，通过Modbus RTU协议实现设备控制
    /// 主要功能包括：
    /// - 温度设置和监测（20-90°C，精度0.1°C）
    /// - 加热控制（开启/停止）
    /// - 加热状态监控
    /// - 报警温度设置
    /// - 实时数据读取
    /// - 设备状态管理
    ///
    /// ==================== 方法目录索引 ====================
    ///
    /// 【构造函数】
    /// - YudianTemperatureControllerDriver(string, int, byte, string, string) // public - 构造函数，初始化温控器驱动
    ///
    /// 【属性】
    /// - DeviceId, Type, DeviceName, Model                    // public    - 设备基本信息属性
    /// - LastCommunicationTime, Status, IsConnected          // public    - 设备状态属性
    /// - CurrentTemperature, TargetTemperature, IsHeating     // public    - 温控器状态属性
    ///
    /// 【事件】
    /// - StatusChanged                                        // public    - 设备状态变化事件
    /// - TemperatureChanged                                   // public    - 温度变化事件
    /// - OnStatusChanged(DeviceStatus, DeviceStatus, string?) // private   - 触发状态变化事件
    /// - OnTemperatureChanged(double, double)                 // private   - 触发温度变化事件
    ///
    /// 【IDevice接口实现】
    /// - ConnectAsync()                                       // public    - 连接到温控器设备
    /// - DisconnectAsync()                                    // public    - 断开温控器连接
    /// - InitializeAsync()                                    // public    - 初始化温控器
    /// - ResetAsync()                                         // public    - 重置温控器
    /// - GetDeviceInfoAsync()                                 // public    - 获取设备详细信息
    ///
    /// 【ITemperatureController接口实现】
    /// - SetTargetTemperatureAsync(double)                    // public    - 设置目标温度
    /// - GetCurrentTemperatureAsync()                         // public    - 获取当前实际温度
    /// - GetTargetTemperatureAsync()                          // public    - 获取目标温度
    /// - StartHeatingAsync()                                  // public    - 开始加热
    /// - StopHeatingAsync()                                   // public    - 停止加热
    /// - GetHeatingStatusAsync()                              // public    - 获取加热状态
    /// - SetAlarmTemperatureAsync(int, double)                // public    - 设置报警温度
    ///
    /// 【私有辅助方法】
    /// - RefreshStatusAsync()                                 // private   - 刷新设备状态
    /// - ValidateTemperature(double)                          // private   - 验证温度范围
    /// - ConvertToTemperature(ushort)                         // private   - 将寄存器值转换为温度
    /// - ConvertFromTemperature(double)                       // private   - 将温度转换为寄存器值
    ///
    /// 【资源管理】
    /// - Dispose(bool)                                        // protected - 释放资源的具体实现
    ///
    /// ====================================================
    /// </remarks>
    public class YudianTemperatureControllerDriver : ModbusRtuBase, ITemperatureController
    {
        private const ushort PV_REGISTER = 0x0000;      // 测量值寄存器
        private const ushort SV_REGISTER = 0x0001;      // 设定值寄存器
        private const ushort LED_REGISTER = 0x0002;     // LED 状态寄存器
        private const ushort OUTB_REGISTER = 0x0003;    // 输出百分比寄存器
        private const ushort AT_ONOFF_REGISTER = 0x0004; // 自整定和开关寄存器
        private const ushort AL1_REGISTER = 0x0005;     // 报警值1寄存器
        private const ushort AL2_REGISTER = 0x0006;     // 报警值2寄存器

        private const double MIN_TEMPERATURE = 20.0;    // 最小温度
        private const double MAX_TEMPERATURE = 90.0;    // 最大温度
        private const double TEMPERATURE_ACCURACY = 0.1; // 温度精度

        private double _currentTemperature = 0.0;
        private double _targetTemperature = 0.0;
        private bool _isHeating = false;

        public YudianTemperatureControllerDriver(
            string portName,
            int baudRate,
            byte slaveAddress,
            string deviceId,
            string deviceName)
            : base(portName, baudRate, slaveAddress)
        {
            DeviceId = deviceId;
            Type = DeviceType.TemperatureController;
            DeviceName = deviceName;
            Model = "宇电MK008";

            App.AlarmService.Debug("温控器驱动", $"初始化宇电温控器驱动 - 设备ID: {deviceId}");
        }

        #region IDevice 属性实现

        public string DeviceId { get; }
        public DeviceType Type { get; }
        public string DeviceName { get; }
        public string Model { get; }
        public DateTime LastCommunicationTime { get; private set; }

        private DeviceStatus _status = DeviceStatus.Disconnected;
        public DeviceStatus Status
        {
            get => _status;
            private set
            {
                if (_status != value)
                {
                    var oldStatus = _status;
                    _status = value;
                    OnStatusChanged(oldStatus, value);
                }
            }
        }

        public bool IsConnected => Status == DeviceStatus.Connected || Status == DeviceStatus.Running;

        #endregion

        #region ITemperatureController 属性实现

        public double CurrentTemperature => _currentTemperature;
        public double TargetTemperature => _targetTemperature;
        public bool IsHeating => _isHeating;

        #endregion

        #region 事件

        public event EventHandler<DeviceStatusChangedEventArgs>? StatusChanged;
        public event EventHandler<TemperatureChangedEventArgs>? TemperatureChanged;

        private void OnStatusChanged(DeviceStatus oldStatus, DeviceStatus newStatus, string? message = null)
        {
            var args = new DeviceStatusChangedEventArgs(DeviceId, oldStatus, newStatus, message);
            StatusChanged?.Invoke(this, args);
            
            App.AlarmService.Info("设备状态", $"温控器 {DeviceName} 状态变更: {oldStatus} -> {newStatus}");
        }

        private void OnTemperatureChanged(double currentTemp, double targetTemp)
        {
            var args = new TemperatureChangedEventArgs(DeviceId, currentTemp, targetTemp);
            TemperatureChanged?.Invoke(this, args);
        }

        #endregion

        #region IDevice 方法实现

        public override async Task<bool> ConnectAsync()
        {
            try
            {
                Status = DeviceStatus.Connecting;
                App.AlarmService.Info("设备连接", $"正在连接温控器 {DeviceName}");

                var connected = await base.ConnectAsync();
                if (!connected)
                {
                    Status = DeviceStatus.Error;
                    return false;
                }

                // 测试通信 - 读取测量值
                var testResult = await ReadHoldingRegistersAsync(PV_REGISTER, 1);
                if (testResult == null)
                {
                    Status = DeviceStatus.Error;
                    App.AlarmService.Error("设备连接", $"温控器 {DeviceName} 通信测试失败");
                    return false;
                }

                Status = DeviceStatus.Connected;
                LastCommunicationTime = DateTime.Now;
                App.AlarmService.Info("设备连接", $"温控器 {DeviceName} 连接成功");

                // 读取初始状态
                await RefreshStatusAsync();

                return true;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"温控器 {DeviceName} 连接异常", ex);
                return false;
            }
        }

        public override async Task<bool> DisconnectAsync()
        {
            try
            {
                var result = await base.DisconnectAsync();
                Status = DeviceStatus.Disconnected;
                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"温控器 {DeviceName} 断开连接异常", ex);
                return false;
            }
        }

        public async Task<bool> InitializeAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    var connected = await ConnectAsync();
                    if (!connected)
                        return false;
                }

                App.AlarmService.Info("设备初始化", $"正在初始化温控器 {DeviceName}");

                // 读取当前状态
                await RefreshStatusAsync();

                App.AlarmService.Info("设备初始化", $"温控器 {DeviceName} 初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备初始化", $"温控器 {DeviceName} 初始化异常", ex);
                return false;
            }
        }

        public async Task<bool> ResetAsync()
        {
            try
            {
                App.AlarmService.Info("设备重置", $"正在重置温控器 {DeviceName}");

                // 停止加热
                await StopHeatingAsync();

                // 重置目标温度为室温
                await SetTargetTemperatureAsync(25.0);

                App.AlarmService.Info("设备重置", $"温控器 {DeviceName} 重置成功");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备重置", $"温控器 {DeviceName} 重置异常", ex);
                return false;
            }
        }

        public async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            try
            {
                // 读取设备状态信息
                var registers = await ReadHoldingRegistersAsync(PV_REGISTER, 5);
                if (registers == null)
                {
                    throw new InvalidOperationException("无法读取设备信息");
                }

                LastCommunicationTime = DateTime.Now;

                return new DeviceInfo
                {
                    DeviceId = DeviceId,
                    DeviceName = DeviceName,
                    Model = Model,
                    Version = "1.0",
                    SerialNumber = $"YD{_slaveAddress:D3}",
                    Properties = new Dictionary<string, object>
                    {
                        ["CurrentTemperature"] = ConvertToTemperature(registers[0]),
                        ["TargetTemperature"] = ConvertToTemperature(registers[1]),
                        ["OutputPercentage"] = registers[3],
                        ["SlaveAddress"] = _slaveAddress,
                        ["MinTemperature"] = MIN_TEMPERATURE,
                        ["MaxTemperature"] = MAX_TEMPERATURE,
                        ["Accuracy"] = TEMPERATURE_ACCURACY
                    }
                };
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备信息", $"获取温控器 {DeviceName} 信息异常", ex);
                throw;
            }
        }

        #endregion

        #region ITemperatureController 方法实现

        public async Task<bool> SetTargetTemperatureAsync(double temperature)
        {
            try
            {
                ValidateTemperature(temperature);

                var registerValue = ConvertFromTemperature(temperature);
                var result = await WriteSingleRegisterAsync(SV_REGISTER, registerValue);

                if (result)
                {
                    _targetTemperature = temperature;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("温度控制", $"温控器 {DeviceName} 目标温度设置为 {temperature:F1}°C");
                    OnTemperatureChanged(_currentTemperature, _targetTemperature);
                }
                else
                {
                    App.AlarmService.Error("温度控制", $"温控器 {DeviceName} 设置目标温度失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("温度控制", $"温控器 {DeviceName} 设置目标温度异常", ex);
                return false;
            }
        }

        public async Task<double> GetCurrentTemperatureAsync()
        {
            try
            {
                var registers = await ReadHoldingRegistersAsync(PV_REGISTER, 1);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取当前温度失败");
                }

                _currentTemperature = ConvertToTemperature(registers[0]);
                LastCommunicationTime = DateTime.Now;

                return _currentTemperature;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("温度读取", $"温控器 {DeviceName} 读取当前温度异常", ex);
                throw;
            }
        }

        public async Task<double> GetTargetTemperatureAsync()
        {
            try
            {
                var registers = await ReadHoldingRegistersAsync(SV_REGISTER, 1);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取目标温度失败");
                }

                _targetTemperature = ConvertToTemperature(registers[0]);
                LastCommunicationTime = DateTime.Now;

                return _targetTemperature;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("温度读取", $"温控器 {DeviceName} 读取目标温度异常", ex);
                throw;
            }
        }

        public async Task<bool> StartHeatingAsync()
        {
            try
            {
                // 写入 0x0A 到 AT/ONOFF 寄存器开启加热
                var result = await WriteSingleRegisterAsync(AT_ONOFF_REGISTER, 0x0A);

                if (result)
                {
                    _isHeating = true;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("加热控制", $"温控器 {DeviceName} 开始加热");
                }
                else
                {
                    App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 启动加热失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 启动加热异常", ex);
                return false;
            }
        }

        public async Task<bool> StopHeatingAsync()
        {
            try
            {
                // 写入 0x14 到 AT/ONOFF 寄存器关闭加热
                var result = await WriteSingleRegisterAsync(AT_ONOFF_REGISTER, 0x14);

                if (result)
                {
                    _isHeating = false;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("加热控制", $"温控器 {DeviceName} 停止加热");
                }
                else
                {
                    App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 停止加热失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 停止加热异常", ex);
                return false;
            }
        }

        public async Task<bool> GetHeatingStatusAsync()
        {
            try
            {
                var registers = await ReadHoldingRegistersAsync(LED_REGISTER, 1);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取加热状态失败");
                }

                // 检查 LED 状态寄存器的 BIT5 (ON/OFF 状态)
                var ledStatus = registers[0];
                _isHeating = (ledStatus & 0x0020) == 0; // BIT5=0 表示加热运行，BIT5=1 表示关闭加热

                LastCommunicationTime = DateTime.Now;
                return _isHeating;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("状态读取", $"温控器 {DeviceName} 读取加热状态异常", ex);
                throw;
            }
        }

        public async Task<bool> SetAlarmTemperatureAsync(int alarmType, double temperature)
        {
            try
            {
                ValidateTemperature(temperature);

                if (alarmType != 1 && alarmType != 2)
                {
                    throw new ArgumentException("报警类型必须是 1 或 2", nameof(alarmType));
                }

                var register = alarmType == 1 ? AL1_REGISTER : AL2_REGISTER;
                var registerValue = ConvertFromTemperature(temperature);
                var result = await WriteSingleRegisterAsync(register, registerValue);

                if (result)
                {
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("报警设置", $"温控器 {DeviceName} 报警{alarmType}温度设置为 {temperature:F1}°C");
                }
                else
                {
                    App.AlarmService.Error("报警设置", $"温控器 {DeviceName} 设置报警{alarmType}温度失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("报警设置", $"温控器 {DeviceName} 设置报警温度异常", ex);
                return false;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        private async Task RefreshStatusAsync()
        {
            try
            {
                // 读取 PV, SV, LED, OUTB 寄存器
                var registers = await ReadHoldingRegistersAsync(PV_REGISTER, 4);
                if (registers != null)
                {
                    var oldCurrentTemp = _currentTemperature;
                    var oldTargetTemp = _targetTemperature;

                    _currentTemperature = ConvertToTemperature(registers[0]);
                    _targetTemperature = ConvertToTemperature(registers[1]);

                    // 检查加热状态
                    var ledStatus = registers[2];
                    _isHeating = (ledStatus & 0x0020) == 0; // BIT5=0 表示加热运行

                    LastCommunicationTime = DateTime.Now;

                    // 如果温度有变化，触发事件
                    if (Math.Abs(_currentTemperature - oldCurrentTemp) > 0.01 ||
                        Math.Abs(_targetTemperature - oldTargetTemp) > 0.01)
                    {
                        OnTemperatureChanged(_currentTemperature, _targetTemperature);
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("状态刷新", $"温控器 {DeviceName} 状态刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证温度范围
        /// </summary>
        private static void ValidateTemperature(double temperature)
        {
            if (temperature < MIN_TEMPERATURE || temperature > MAX_TEMPERATURE)
            {
                throw new ArgumentOutOfRangeException(nameof(temperature),
                    $"温度必须在 {MIN_TEMPERATURE}°C 到 {MAX_TEMPERATURE}°C 之间，当前值: {temperature}°C");
            }
        }

        /// <summary>
        /// 将寄存器值转换为温度
        /// </summary>
        private static double ConvertToTemperature(ushort registerValue)
        {
            // 处理二进制补码
            short signedValue = (short)registerValue;

            // 检查溢出标志
            if (signedValue == -16666) // LLLL (下溢出)
            {
                return double.NegativeInfinity;
            }
            if (signedValue == 18888) // HHHH (上溢出)
            {
                return double.PositiveInfinity;
            }

            // 温度值需要除以10（设备内部存储时乘以10）
            return signedValue / 10.0;
        }

        /// <summary>
        /// 将温度转换为寄存器值
        /// </summary>
        private static ushort ConvertFromTemperature(double temperature)
        {
            // 温度值需要乘以10发送给设备
            var value = (short)(temperature * 10);
            return (ushort)value;
        }

        #endregion

        #region IDisposable 实现

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 停止加热
                    StopHeatingAsync().Wait(2000);
                }
                catch (Exception ex)
                {
                    App.AlarmService.Error("温控器驱动", "释放温控器资源时停止加热失败", ex);
                }
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
