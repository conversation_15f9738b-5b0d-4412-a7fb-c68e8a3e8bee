﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D424D8BE63CE5E6B5EEE012C8CFA4322BD3AEF8C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using PEMTestSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PEMTestSystem {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 327 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse TempControllerStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse Pump1StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse Pump2StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse PowerSupplyStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VoltageValueText;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentValueText;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TemperatureValueText;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowRate1ValueText;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowRate2ValueText;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PowerVoltageValueText;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PowerCurrentValueText;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChartTitleText;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart RealTimeChart;
        
        #line default
        #line hidden
        
        
        #line 457 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartButton;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopButton;
        
        #line default
        #line hidden
        
        
        #line 590 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PowerVoltageSetBox;
        
        #line default
        #line hidden
        
        
        #line 596 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PowerCurrentSetBox;
        
        #line default
        #line hidden
        
        
        #line 602 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PowerModeCombo;
        
        #line default
        #line hidden
        
        
        #line 610 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerSetButton;
        
        #line default
        #line hidden
        
        
        #line 612 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerOnButton;
        
        #line default
        #line hidden
        
        
        #line 614 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerOffButton;
        
        #line default
        #line hidden
        
        
        #line 621 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PowerOutputStatusText;
        
        #line default
        #line hidden
        
        
        #line 624 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PowerWorkingModeText;
        
        #line default
        #line hidden
        
        
        #line 648 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl ExperimentModeTabControl;
        
        #line default
        #line hidden
        
        
        #line 809 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ScanByRateRadio;
        
        #line default
        #line hidden
        
        
        #line 810 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ScanByTimeRadio;
        
        #line default
        #line hidden
        
        
        #line 813 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScanRateGroup;
        
        #line default
        #line hidden
        
        
        #line 821 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScanTimeGroup;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PEMTestSystem;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TempControllerStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 2:
            this.Pump1StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 3:
            this.Pump2StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 4:
            this.PowerSupplyStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 5:
            this.VoltageValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CurrentValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TemperatureValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.FlowRate1ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.FlowRate2ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PowerVoltageValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.PowerCurrentValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ChartTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 423 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportChart_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 425 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowHistory_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RealTimeChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 16:
            this.StartButton = ((System.Windows.Controls.Button)(target));
            
            #line 458 "..\..\..\MainWindow.xaml"
            this.StartButton.Click += new System.Windows.RoutedEventHandler(this.StartButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.StopButton = ((System.Windows.Controls.Button)(target));
            
            #line 461 "..\..\..\MainWindow.xaml"
            this.StopButton.Click += new System.Windows.RoutedEventHandler(this.StopButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 476 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeviceSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PowerVoltageSetBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.PowerCurrentSetBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.PowerModeCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.PowerSetButton = ((System.Windows.Controls.Button)(target));
            
            #line 611 "..\..\..\MainWindow.xaml"
            this.PowerSetButton.Click += new System.Windows.RoutedEventHandler(this.PowerSetButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.PowerOnButton = ((System.Windows.Controls.Button)(target));
            
            #line 613 "..\..\..\MainWindow.xaml"
            this.PowerOnButton.Click += new System.Windows.RoutedEventHandler(this.PowerOnButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.PowerOffButton = ((System.Windows.Controls.Button)(target));
            
            #line 615 "..\..\..\MainWindow.xaml"
            this.PowerOffButton.Click += new System.Windows.RoutedEventHandler(this.PowerOffButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.PowerOutputStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.PowerWorkingModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.ExperimentModeTabControl = ((System.Windows.Controls.TabControl)(target));
            
            #line 651 "..\..\..\MainWindow.xaml"
            this.ExperimentModeTabControl.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ExperimentModeTabControl_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ScanByRateRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 809 "..\..\..\MainWindow.xaml"
            this.ScanByRateRadio.Checked += new System.Windows.RoutedEventHandler(this.ScanMethod_Checked);
            
            #line default
            #line hidden
            return;
            case 30:
            this.ScanByTimeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 810 "..\..\..\MainWindow.xaml"
            this.ScanByTimeRadio.Checked += new System.Windows.RoutedEventHandler(this.ScanMethod_Checked);
            
            #line default
            #line hidden
            return;
            case 31:
            this.ScanRateGroup = ((System.Windows.Controls.Grid)(target));
            return;
            case 32:
            this.ScanTimeGroup = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 19:
            
            #line 524 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ToggleDeviceState_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

