using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

using Newtonsoft.Json;
using PEMTestSystem.Data;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 设备管理器
    /// 负责管理所有设备的连接、状态监控和通信协调，提供统一的设备管理接口
    /// </summary>
    /// <remarks>
    /// 该管理器负责整个系统的设备生命周期管理，包括：
    /// - 从数据库加载设备配置
    /// - 创建和初始化设备实例
    /// - 管理设备连接和状态
    /// - 提供设备访问接口
    /// - 监控设备健康状态
    /// - 处理设备重连逻辑
    ///
    /// ==================== 方法目录索引 ====================
    ///
    /// 【构造函数】
    /// - DeviceManager(IServiceProvider, SerialPortManager)   // public    - 构造函数，初始化设备管理器
    ///
    /// 【设备注册和管理】
    /// - LoadDeviceConfigurationsAsync()                      // public    - 从数据库加载设备配置
    /// - InitializeAllDevicesAsync()                          // public    - 初始化所有设备
    /// - InitializeDeviceAsync(DeviceConfiguration)           // private   - 初始化单个设备
    /// - CreateModbusDeviceAsync(DeviceConfiguration)         // private   - 创建Modbus设备实例
    /// - CreateSerialPortDeviceAsync(DeviceConfiguration)     // private   - 创建串口设备实例
    ///
    /// 【设备访问】
    /// - GetDevice<T>(string)                                 // public    - 获取指定类型的设备
    /// - GetTemperatureController(string)                     // public    - 获取温控器设备
    /// - GetFlowPump(string)                                  // public    - 获取流量泵设备
    /// - GetPowerSupply(string)                               // public    - 获取电源设备
    /// - GetAllDevices()                                      // public    - 获取所有设备
    /// - GetDevicesByType<T>()                                // public    - 获取指定类型的所有设备
    /// - GetAllDeviceStatus()                                 // public    - 获取所有设备状态信息
    ///
    /// 【设备控制】
    /// - ConnectAllDevicesAsync()                             // public    - 连接所有设备
    /// - DisconnectAllDevicesAsync()                          // public    - 断开所有设备
    /// - ResetAllDevicesAsync()                               // public    - 重置所有设备
    ///
    /// 【事件处理】
    /// - DeviceStatusChanged                                  // public    - 设备状态变化事件
    /// - OnDeviceStatusChanged(object?, DeviceStatusChangedEventArgs) // private - 处理设备状态变化
    /// - TryReconnectDeviceAsync(string)                      // private   - 尝试重连设备
    ///
    /// 【健康检查和监控】
    /// - CheckAllDevicesHealthAsync()                         // public    - 检查所有设备健康状态
    /// - GetCommunicationQualityReport()                      // public    - 获取串口通信质量报告
    ///
    /// 【资源管理】
    /// - Dispose()                                            // public    - 释放资源
    /// - Dispose(bool)                                        // protected - 释放资源的具体实现
    ///
    /// ====================================================
    /// </remarks>
    public class DeviceManager : IDisposable
    {

        private readonly IServiceProvider _serviceProvider;
        private readonly SerialPortManager _serialPortManager;
        private readonly ConcurrentDictionary<string, IDevice> _devices;
        private readonly ConcurrentDictionary<string, DeviceConfiguration> _deviceConfigurations;
        private bool _disposed = false;

        public DeviceManager(
            IServiceProvider serviceProvider,
            SerialPortManager serialPortManager)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _serialPortManager = serialPortManager ?? throw new ArgumentNullException(nameof(serialPortManager));
            
            _devices = new ConcurrentDictionary<string, IDevice>();
            _deviceConfigurations = new ConcurrentDictionary<string, DeviceConfiguration>();

            App.AlarmService.Info("设备管理", "设备管理器初始化完成");
        }

        #region 设备注册和管理

        /// <summary>
        /// 从数据库加载设备配置
        /// </summary>
        public async Task<bool> LoadDeviceConfigurationsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<PEMTestDbContext>();

                var devices = await context.Devices
                    .Where(d => d.IsActive)
                    .ToListAsync();

                foreach (var device in devices)
                {
                    var config = new DeviceConfiguration
                    {
                        DeviceId = device.DeviceId,
                        DeviceName = device.DeviceName,
                        Model = device.Model,
                        DeviceType = device.DeviceType,
                        ConnectionType = device.ConnectionType,
                        ConnectionString = device.ConnectionString,
                        Specifications = device.Specifications
                    };

                    _deviceConfigurations[device.DeviceId] = config;
                }

                App.AlarmService.Info("设备管理", $"加载了 {devices.Count} 个设备配置");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "加载设备配置失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化所有设备
        /// </summary>
        public async Task<bool> InitializeAllDevicesAsync()
        {
            try
            {
                var initializationTasks = new List<Task<bool>>();

                foreach (var config in _deviceConfigurations.Values)
                {
                    initializationTasks.Add(InitializeDeviceAsync(config));
                }

                var results = await Task.WhenAll(initializationTasks);
                var successCount = results.Count(r => r);

                App.AlarmService.Info("设备管理", $"设备初始化完成，成功: {successCount}/{results.Length}");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "初始化设备失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化单个设备
        /// </summary>
        private async Task<bool> InitializeDeviceAsync(DeviceConfiguration config)
        {
            try
            {
                // 如果设备已存在，先释放
                if (_devices.TryGetValue(config.DeviceId, out var existingDevice))
                {
                    existingDevice.Dispose();
                    _devices.TryRemove(config.DeviceId, out _);
                }

                IDevice? device = null;

                // 根据设备类型和连接类型创建设备实例
                if (config.ConnectionType == ConnectionType.ModbusRTU)
                {
                    device = await CreateModbusDeviceAsync(config);
                }
                else if (config.ConnectionType == ConnectionType.SerialPort)
                {
                    device = await CreateSerialPortDeviceAsync(config);
                }
                else if (config.ConnectionType == ConnectionType.Ethernet)
                {
                    // TODO: 创建以太网设备（如电源）
                    App.AlarmService.Warning("设备管理", $"暂不支持以太网设备类型: {config.DeviceType}");
                    return false;
                }

                if (device == null)
                {
                    App.AlarmService.Error("设备管理", $"无法创建设备 {config.DeviceId}");
                    return false;
                }

                // 订阅设备状态变化事件
                device.StatusChanged += OnDeviceStatusChanged;

                // 添加到设备集合
                _devices[config.DeviceId] = device;

                // 初始化设备
                var initialized = await device.InitializeAsync();
                if (initialized)
                {
                    App.AlarmService.Info("设备管理", $"设备 {config.DeviceName} 初始化成功");
                }
                else
                {
                    App.AlarmService.Error("设备管理", $"设备 {config.DeviceName} 初始化失败");
                }

                return initialized;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", $"初始化设备 {config.DeviceId} 异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 创建 Modbus 设备
        /// </summary>
        private async Task<IDevice?> CreateModbusDeviceAsync(DeviceConfiguration config)
        {
            try
            {
                // 解析连接字符串
                var connectionInfo = JsonConvert.DeserializeObject<ModbusConnectionInfo>(config.ConnectionString);
                if (connectionInfo == null)
                {
                    App.AlarmService.Error("设备管理", $"设备 {config.DeviceId} 连接字符串解析失败");
                    return null;
                }

                // 注册到串口管理器
                var registered = await _serialPortManager.RegisterDeviceAsync(
                    config.DeviceId, 
                    connectionInfo.Port, 
                    connectionInfo.BaudRate, 
                    (byte)connectionInfo.Address);

                if (!registered)
                {
                    App.AlarmService.Error("设备管理", $"设备 {config.DeviceId} 串口注册失败");
                    return null;
                }

                // 创建设备实例
                switch (config.DeviceType)
                {
                    case DeviceType.TemperatureController:
                        return new YudianTemperatureControllerDriver(
                            connectionInfo.Port,
                            connectionInfo.BaudRate,
                            (byte)connectionInfo.Address,
                            config.DeviceId,
                            config.DeviceName);

                    case DeviceType.Pump1:
                    case DeviceType.Pump2:
                        return new KachuanFlowPumpDriver(
                            connectionInfo.Port,
                            connectionInfo.BaudRate,
                            (byte)connectionInfo.Address,
                            config.DeviceId,
                            config.DeviceName);

                    default:
                        App.AlarmService.Warning("设备管理", $"不支持的 Modbus 设备类型: {config.DeviceType}");
                        return null;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", $"创建 Modbus 设备 {config.DeviceId} 异常", ex);
                return null;
            }
        }

        /// <summary>
        /// 创建串口设备（如SCPI电源）
        /// </summary>
        private async Task<IDevice?> CreateSerialPortDeviceAsync(DeviceConfiguration config)
        {
            try
            {
                // 解析连接字符串
                var connectionInfo = JsonConvert.DeserializeObject<SerialPortConnectionInfo>(config.ConnectionString);
                if (connectionInfo == null)
                {
                    App.AlarmService.Error("设备管理", $"设备 {config.DeviceId} 串口连接字符串解析失败");
                    return null;
                }

                // 创建设备实例
                switch (config.DeviceType)
                {
                    case DeviceType.PowerSupply:
                        return new ItechPowerSupplyDriver(
                            connectionInfo.Port,
                            connectionInfo.BaudRate,
                            config.DeviceId,
                            config.DeviceName);

                    default:
                        App.AlarmService.Warning("设备管理", $"不支持的串口设备类型: {config.DeviceType}");
                        return null;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", $"创建串口设备 {config.DeviceId} 异常", ex);
                return null;
            }
        }

        #endregion

        #region 设备访问

        /// <summary>
        /// 获取设备
        /// </summary>
        public T? GetDevice<T>(string deviceId) where T : class, IDevice
        {
            return _devices.TryGetValue(deviceId, out var device) ? device as T : null;
        }

        /// <summary>
        /// 获取温控器
        /// </summary>
        public ITemperatureController? GetTemperatureController(string deviceId)
        {
            return GetDevice<ITemperatureController>(deviceId);
        }

        /// <summary>
        /// 获取流量泵
        /// </summary>
        public IFlowPump? GetFlowPump(string deviceId)
        {
            return GetDevice<IFlowPump>(deviceId);
        }

        /// <summary>
        /// 获取电源
        /// </summary>
        public IPowerSupply? GetPowerSupply(string deviceId)
        {
            return GetDevice<IPowerSupply>(deviceId);
        }

        /// <summary>
        /// 获取所有设备
        /// </summary>
        public IEnumerable<IDevice> GetAllDevices()
        {
            return _devices.Values.ToList();
        }

        /// <summary>
        /// 获取指定类型的所有设备
        /// </summary>
        public IEnumerable<T> GetDevicesByType<T>() where T : class, IDevice
        {
            return _devices.Values.OfType<T>().ToList();
        }

        /// <summary>
        /// 获取设备状态
        /// </summary>
        public Dictionary<string, DeviceStatusInfo> GetAllDeviceStatus()
        {
            var status = new Dictionary<string, DeviceStatusInfo>();

            foreach (var kvp in _devices)
            {
                var device = kvp.Value;
                status[kvp.Key] = new DeviceStatusInfo
                {
                    DeviceId = device.DeviceId,
                    DeviceName = device.DeviceName,
                    DeviceType = device.Type,
                    Status = device.Status,
                    IsConnected = device.IsConnected,
                    LastCommunicationTime = device.LastCommunicationTime
                };
            }

            return status;
        }

        #endregion

        #region 设备控制

        /// <summary>
        /// 连接所有设备
        /// </summary>
        public async Task<bool> ConnectAllDevicesAsync()
        {
            try
            {
                var connectTasks = _devices.Values.Select(device => device.ConnectAsync()).ToArray();
                var results = await Task.WhenAll(connectTasks);
                var successCount = results.Count(r => r);

                App.AlarmService.Info("设备管理", $"设备连接完成，成功: {successCount}/{results.Length}");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "连接设备失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 断开所有设备
        /// </summary>
        public async Task<bool> DisconnectAllDevicesAsync()
        {
            try
            {
                var disconnectTasks = _devices.Values.Select(device => device.DisconnectAsync()).ToArray();
                var results = await Task.WhenAll(disconnectTasks);
                var successCount = results.Count(r => r);

                App.AlarmService.Info("设备管理", $"设备断开完成，成功: {successCount}/{results.Length}");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "断开设备失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 重置所有设备
        /// </summary>
        public async Task<bool> ResetAllDevicesAsync()
        {
            try
            {
                var resetTasks = _devices.Values.Select(device => device.ResetAsync()).ToArray();
                var results = await Task.WhenAll(resetTasks);
                var successCount = results.Count(r => r);

                App.AlarmService.Info("设备管理", $"设备重置完成，成功: {successCount}/{results.Length}");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "重置设备失败", ex);
                return false;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        public event EventHandler<DeviceStatusChangedEventArgs>? DeviceStatusChanged;

        private void OnDeviceStatusChanged(object? sender, DeviceStatusChangedEventArgs e)
        {
            try
            {
                // 转发设备状态变化事件
                DeviceStatusChanged?.Invoke(this, e);

                // 记录状态变化
                App.AlarmService.Info("设备状态", $"设备 {e.DeviceId} 状态变更: {e.OldStatus} -> {e.NewStatus}");

                // 如果设备连接丢失，尝试重连
                if (e.NewStatus == DeviceStatus.Disconnected && e.OldStatus == DeviceStatus.Connected)
                {
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(5000); // 等待5秒后重连
                        await TryReconnectDeviceAsync(e.DeviceId);
                    });
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "处理设备状态变化事件异常", ex);
            }
        }

        /// <summary>
        /// 尝试重连设备
        /// </summary>
        private async Task TryReconnectDeviceAsync(string deviceId)
        {
            try
            {
                if (_devices.TryGetValue(deviceId, out var device))
                {
                    App.AlarmService.Info("设备管理", $"尝试重连设备 {device.DeviceName}");

                    var reconnected = await device.ConnectAsync();
                    if (reconnected)
                    {
                        App.AlarmService.Info("设备管理", $"设备 {device.DeviceName} 重连成功");
                    }
                    else
                    {
                        App.AlarmService.Warning("设备管理", $"设备 {device.DeviceName} 重连失败");
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", $"重连设备 {deviceId} 异常", ex);
            }
        }

        #endregion

        #region 健康检查和监控

        /// <summary>
        /// 检查所有设备健康状态
        /// </summary>
        public async Task<Dictionary<string, bool>> CheckAllDevicesHealthAsync()
        {
            var healthStatus = new Dictionary<string, bool>();

            try
            {
                var healthTasks = _devices.Select(async kvp =>
                {
                    try
                    {
                        var isConnected = await kvp.Value.IsConnectedAsync();
                        return new { DeviceId = kvp.Key, IsHealthy = isConnected };
                    }
                    catch
                    {
                        return new { DeviceId = kvp.Key, IsHealthy = false };
                    }
                }).ToArray();

                var results = await Task.WhenAll(healthTasks);

                foreach (var result in results)
                {
                    healthStatus[result.DeviceId] = result.IsHealthy;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备管理", "检查设备健康状态异常", ex);
            }

            return healthStatus;
        }

        /// <summary>
        /// 获取串口通信质量报告
        /// </summary>
        public Dictionary<string, SerialPortStatus> GetCommunicationQualityReport()
        {
            return _serialPortManager.GetAllPortStatus();
        }

        #endregion

        #region IDisposable 实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 断开所有设备
                        DisconnectAllDevicesAsync().Wait(10000);

                        // 释放所有设备
                        foreach (var device in _devices.Values)
                        {
                            device.Dispose();
                        }

                        _devices.Clear();
                        _deviceConfigurations.Clear();

                        App.AlarmService.Info("设备管理", "设备管理器已释放");
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("设备管理", "释放设备管理器资源时发生错误", ex);
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// 设备配置信息
    /// </summary>
    public class DeviceConfiguration
    {
        public string DeviceId { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public DeviceType DeviceType { get; set; }
        public ConnectionType ConnectionType { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public string? Specifications { get; set; }
    }

    /// <summary>
    /// Modbus 连接信息
    /// </summary>
    public class ModbusConnectionInfo
    {
        public string Port { get; set; } = string.Empty;
        public int BaudRate { get; set; }
        public int Address { get; set; }
    }

    /// <summary>
    /// 串口连接信息（用于SCPI等串口设备）
    /// </summary>
    public class SerialPortConnectionInfo
    {
        public string Port { get; set; } = string.Empty;
        public int BaudRate { get; set; } = 9600;
        public int Timeout { get; set; } = 3000;
    }

    /// <summary>
    /// 设备状态信息
    /// </summary>
    public class DeviceStatusInfo
    {
        public string DeviceId { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public DeviceType DeviceType { get; set; }
        public DeviceStatus Status { get; set; }
        public bool IsConnected { get; set; }
        public DateTime LastCommunicationTime { get; set; }
    }
}
