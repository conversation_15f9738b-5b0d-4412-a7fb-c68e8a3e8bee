﻿#pragma checksum "..\..\..\DeviceSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "ECBF8EA4B3E0810D5A006D991B1A9794F22EC9BD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using PEMTestSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PEMTestSystem {
    
    
    /// <summary>
    /// DeviceSettingsWindow
    /// </summary>
    public partial class DeviceSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 289 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl DeviceTabControl;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PowerPortCombo;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PowerBaudRateCombo;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PowerDataBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PowerStopBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PowerParityCombo;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PowerAddressBox;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestPowerButton;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PowerStatusText;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TempControllerPortCombo;
        
        #line default
        #line hidden
        
        
        #line 379 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TempControllerBaudRateCombo;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TempControllerDataBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TempControllerStopBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TempControllerParityCombo;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TempControllerAddressBox;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestTempControllerButton;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TempControllerStatusText;
        
        #line default
        #line hidden
        
        
        #line 438 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump1PortCombo;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump1BaudRateCombo;
        
        #line default
        #line hidden
        
        
        #line 450 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump1DataBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump1StopBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump1ParityCombo;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FlowPump1AddressBox;
        
        #line default
        #line hidden
        
        
        #line 473 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestFlowPump1Button;
        
        #line default
        #line hidden
        
        
        #line 474 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowPump1StatusText;
        
        #line default
        #line hidden
        
        
        #line 500 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump2PortCombo;
        
        #line default
        #line hidden
        
        
        #line 503 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump2BaudRateCombo;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump2DataBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 518 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump2StopBitsCombo;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FlowPump2ParityCombo;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FlowPump2AddressBox;
        
        #line default
        #line hidden
        
        
        #line 535 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestFlowPump2Button;
        
        #line default
        #line hidden
        
        
        #line 536 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowPump2StatusText;
        
        #line default
        #line hidden
        
        
        #line 561 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseServerBox;
        
        #line default
        #line hidden
        
        
        #line 564 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseNameBox;
        
        #line default
        #line hidden
        
        
        #line 567 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseUserBox;
        
        #line default
        #line hidden
        
        
        #line 570 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox DatabasePasswordBox;
        
        #line default
        #line hidden
        
        
        #line 597 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UseWindowsAuthCheckBox;
        
        #line default
        #line hidden
        
        
        #line 601 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestDatabaseButton;
        
        #line default
        #line hidden
        
        
        #line 602 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatabaseStatusText;
        
        #line default
        #line hidden
        
        
        #line 612 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 613 "..\..\..\DeviceSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PEMTestSystem;V1.0.0.0;component/devicesettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\DeviceSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 2:
            this.PowerPortCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.PowerBaudRateCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.PowerDataBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.PowerStopBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.PowerParityCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.PowerAddressBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.TestPowerButton = ((System.Windows.Controls.Button)(target));
            
            #line 349 "..\..\..\DeviceSettingsWindow.xaml"
            this.TestPowerButton.Click += new System.Windows.RoutedEventHandler(this.TestPowerButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PowerStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TempControllerPortCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.TempControllerBaudRateCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.TempControllerDataBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.TempControllerStopBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.TempControllerParityCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.TempControllerAddressBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.TestTempControllerButton = ((System.Windows.Controls.Button)(target));
            
            #line 411 "..\..\..\DeviceSettingsWindow.xaml"
            this.TestTempControllerButton.Click += new System.Windows.RoutedEventHandler(this.TestTempControllerButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.TempControllerStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.FlowPump1PortCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.FlowPump1BaudRateCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            this.FlowPump1DataBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 21:
            this.FlowPump1StopBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.FlowPump1ParityCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.FlowPump1AddressBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.TestFlowPump1Button = ((System.Windows.Controls.Button)(target));
            
            #line 473 "..\..\..\DeviceSettingsWindow.xaml"
            this.TestFlowPump1Button.Click += new System.Windows.RoutedEventHandler(this.TestFlowPump1Button_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.FlowPump1StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.FlowPump2PortCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 27:
            this.FlowPump2BaudRateCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 28:
            this.FlowPump2DataBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 29:
            this.FlowPump2StopBitsCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 30:
            this.FlowPump2ParityCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 31:
            this.FlowPump2AddressBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.TestFlowPump2Button = ((System.Windows.Controls.Button)(target));
            
            #line 535 "..\..\..\DeviceSettingsWindow.xaml"
            this.TestFlowPump2Button.Click += new System.Windows.RoutedEventHandler(this.TestFlowPump2Button_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.FlowPump2StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.DatabaseServerBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 35:
            this.DatabaseNameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.DatabaseUserBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 37:
            this.DatabasePasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 38:
            this.UseWindowsAuthCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 597 "..\..\..\DeviceSettingsWindow.xaml"
            this.UseWindowsAuthCheckBox.Checked += new System.Windows.RoutedEventHandler(this.UseWindowsAuth_Checked);
            
            #line default
            #line hidden
            
            #line 597 "..\..\..\DeviceSettingsWindow.xaml"
            this.UseWindowsAuthCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.UseWindowsAuth_Unchecked);
            
            #line default
            #line hidden
            return;
            case 39:
            this.TestDatabaseButton = ((System.Windows.Controls.Button)(target));
            
            #line 601 "..\..\..\DeviceSettingsWindow.xaml"
            this.TestDatabaseButton.Click += new System.Windows.RoutedEventHandler(this.TestDatabaseButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.DatabaseStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 612 "..\..\..\DeviceSettingsWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 613 "..\..\..\DeviceSettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

